import { ref, computed } from 'vue'
import { authService } from '../services/auth'
import api from '../services/api'

// 全局用户状态
const user = ref(null)
const isLoading = ref(false)

// 全局计算属性
const isAuthenticated = computed(() => !!user.value)

// 初始化用户状态
const initializeAuth = () => {
  const currentUser = authService.getCurrentUser()
  if (currentUser) {
    user.value = currentUser
  }
}

// 初始化
initializeAuth()

// 全局状态更新函数
const updateGlobalUser = (newUserData) => {
  if (newUserData) {
    user.value = { ...user.value, ...newUserData }
    // 同时更新localStorage
    authService.updateStoredUser(user.value)
  }
}

const setGlobalUser = (userData) => {
  user.value = userData
  if (userData) {
    authService.updateStoredUser(userData)
  }
}

export function useAuth() {
  
  // 登录
  const login = async (username, password) => {
    isLoading.value = true
    try {
      const result = await authService.login(username, password)
      user.value = result.user
      return result
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (username, email, password) => {
    isLoading.value = true
    try {
      const result = await authService.register(username, email, password)
      user.value = result.user
      return result
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    isLoading.value = true
    try {
      await authService.logout()
      user.value = null
    } catch (error) {
      console.error('登出失败:', error)
      // 即使登出请求失败，也要清除本地状态
      user.value = null
    } finally {
      isLoading.value = false
    }
  }

  // 验证token
  const verifyToken = async () => {
    if (!authService.getToken()) {
      return false
    }
    
    try {
      const userData = await authService.verifyToken()
      user.value = userData
      return true
    } catch (error) {
      user.value = null
      return false
    }
  }

  // 刷新用户信息
  const refreshUser = () => {
    const currentUser = authService.getCurrentUser()
    user.value = currentUser
  }

  // 更新用户信息（用于其他组件调用）
  const updateUser = (newUserData) => {
    updateGlobalUser(newUserData)
  }

  // 重新验证并刷新用户信息
  const reloadUser = async () => {
    if (authService.getToken()) {
      try {
        // 调用验证接口获取最新用户信息
        const response = await api.get('/auth/verify')
        if (response.success) {
          const userData = response.data.user
          setGlobalUser(userData)
          return userData
        } else {
          throw new Error('验证失败')
        }
      } catch (error) {
        console.error('重新加载用户信息失败:', error)
        user.value = null
        return null
      }
    }
    return null
  }

  // 刷新JWT token
  const refreshToken = async () => {
    try {
      const result = await authService.refreshToken()
      user.value = result.user
      return result
    } catch (error) {
      console.error('刷新Token失败:', error)
      user.value = null
      throw error
    }
  }

  return {
    // 状态
    user: computed(() => user.value),
    isAuthenticated,
    isLoading: computed(() => isLoading.value),

    // 方法
    login,
    register,
    logout,
    verifyToken,
    refreshUser,
    updateUser,
    reloadUser,
    refreshToken
  }
}

// 导出全局状态更新函数供其他模块使用
export { updateGlobalUser, setGlobalUser }
