# CTA区域根据登录状态显示不同内容的修复

## 问题描述
在首页底部的CTA（Call-to-Action）区域，无论用户是否已登录，都显示"免费注册"按钮，这对已登录用户来说是不合适的。

## 解决方案
修改 `Home.vue` 组件，根据用户登录状态显示不同的CTA内容：

### 未登录用户看到：
- 标题：**"准备开始了吗？"**
- 副标题：**"立即注册账户，开始购买EDU邮箱"**
- 按钮：**[免费注册]**

### 已登录用户看到：
- 标题：**"开始购买吧！"**
- 副标题：**"欢迎回来，{用户名}！浏览我们的商品，找到您需要的EDU邮箱"**
- 按钮：**[浏览商品]** 和 **[查看购物车]**

## 技术实现

### 1. 模板修改
```vue
<!-- CTA Section -->
<section class="cta-section">
  <div class="container">
    <div class="cta-content">
      <!-- 未登录用户显示注册CTA -->
      <template v-if="!isAuthenticated">
        <h2 class="cta-title">准备开始了吗？</h2>
        <p class="cta-subtitle">立即注册账户，开始购买EDU邮箱</p>
        <div class="cta-actions">
          <router-link to="/register" class="btn btn-primary cta-btn">
            免费注册
          </router-link>
        </div>
      </template>
      
      <!-- 已登录用户显示购物CTA -->
      <template v-else>
        <h2 class="cta-title">开始购买吧！</h2>
        <p class="cta-subtitle">欢迎回来，{{ user?.username }}！浏览我们的商品，找到您需要的EDU邮箱</p>
        <div class="cta-actions">
          <router-link to="/products" class="btn btn-primary cta-btn">
            浏览商品
          </router-link>
          <router-link to="/cart" class="btn btn-accent cta-btn">
            查看购物车
          </router-link>
        </div>
      </template>
    </div>
  </div>
</section>
```

### 2. 脚本修改
```javascript
import { useAuth } from '../composables/useAuth'

export default {
  setup() {
    const { user, isAuthenticated } = useAuth()
    
    return {
      user,
      isAuthenticated,
      // ... 其他返回值
    }
  }
}
```

### 3. 样式优化
- 为多个按钮添加了 flexbox 布局
- 添加了响应式设计支持
- 确保按钮在移动设备上正确显示

## 测试方法

### 未登录状态测试：
1. 访问 http://localhost:5173
2. 确保未登录（右上角显示"登录"和"注册"按钮）
3. 滚动到页面底部
4. 应该看到"准备开始了吗？"和"免费注册"按钮

### 已登录状态测试：
1. 登录到系统
2. 访问首页
3. 滚动到页面底部
4. 应该看到"开始购买吧！"、个性化欢迎信息和两个按钮

## 用户体验改进
- ✅ 避免向已登录用户显示注册按钮
- ✅ 提供个性化的欢迎信息
- ✅ 引导已登录用户进行购买行为
- ✅ 提供快速访问购物车的入口
- ✅ 保持界面的一致性和美观性
