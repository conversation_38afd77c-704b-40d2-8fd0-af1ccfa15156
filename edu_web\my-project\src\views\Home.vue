<template>
  <div class="home-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">EDU邮箱自动售卖平台</h1>
            <p class="hero-subtitle">
              快速获取各大高校EDU邮箱，享受教育优惠和学术资源
            </p>
            <div class="hero-features">
              <div class="feature-item">
                <span class="feature-icon">⚡</span>
                <span class="feature-text">即时发货</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">🔒</span>
                <span class="feature-text">安全可靠</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">💎</span>
                <span class="feature-text">优质货源</span>
              </div>
            </div>
            <div class="hero-actions">
              <router-link to="/products" class="btn btn-primary hero-btn">
                浏览商品
              </router-link>
              <router-link to="/register" class="btn btn-accent hero-btn">
                立即注册
              </router-link>
            </div>
          </div>

          <div class="hero-visual">
            <div class="hero-window">
              <div class="window-header">
                <div class="window-controls">
                  <span class="control-dot"></span>
                  <span class="control-dot"></span>
                  <span class="control-dot"></span>
                </div>
                <span class="window-title">EDU邮箱商店</span>
              </div>
              <div class="window-content">
                <div v-if="isLoadingProducts" class="loading-products">
                  <p>正在加载商品...</p>
                </div>
                <div v-else-if="hotProducts.length > 0">
                  <div
                    v-for="product in hotProducts.slice(0, 2)"
                    :key="product.id"
                    class="mock-product"
                    @click="goToProduct(product.id)"
                  >
                    <div class="product-logo">
                      <img :src="product.school_logo_url" :alt="product.name" />
                    </div>
                    <div class="product-info">
                      <h3>{{ product.name }}</h3>
                      <p class="product-price">¥{{ product.price }}</p>
                      <span class="product-status">
                        {{ product.inventory.in_stock ? `有货 (${product.inventory.available})` : '缺货' }}
                      </span>
                    </div>
                  </div>
                </div>
                <div v-else class="no-products">
                  <p>暂无商品</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 装饰性图形 -->
      <div class="hero-decorations">
        <div class="decoration-grid"></div>
        <div class="decoration-shape shape-1"></div>
        <div class="decoration-shape shape-2"></div>
        <div class="decoration-shape shape-3"></div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title">为什么选择我们？</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-header">
              <div class="feature-icon-large">🚀</div>
              <h3 class="feature-title">自动发货</h3>
            </div>
            <p class="feature-description">
              支付成功后立即自动发货，无需等待人工处理，24小时全天候服务
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-header">
              <div class="feature-icon-large">🛡️</div>
              <h3 class="feature-title">安全保障</h3>
            </div>
            <p class="feature-description">
              所有邮箱均经过严格测试，确保可用性，提供质保服务
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-header">
              <div class="feature-icon-large">💰</div>
              <h3 class="feature-title">价格优惠</h3>
            </div>
            <p class="feature-description">
              直接对接货源，省去中间环节，为您提供最优惠的价格
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <!-- 未登录用户显示注册CTA -->
          <template v-if="!isAuthenticated">
            <h2 class="cta-title">准备开始了吗？</h2>
            <p class="cta-subtitle">立即注册账户，开始购买EDU邮箱</p>
            <div class="cta-actions">
              <router-link to="/register" class="btn btn-primary cta-btn">
                免费注册
              </router-link>
            </div>
          </template>

          <!-- 已登录用户显示购物CTA -->
          <template v-else>
            <h2 class="cta-title">开始购买吧！</h2>
            <p class="cta-subtitle">欢迎回来，{{ user?.username }}！浏览我们的商品，找到您需要的EDU邮箱</p>
            <div class="cta-actions">
              <router-link to="/products" class="btn btn-primary cta-btn">
                浏览商品
              </router-link>
              <router-link to="/cart" class="btn btn-accent cta-btn">
                查看购物车
              </router-link>
            </div>
          </template>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { productService } from '../services/product'

export default {
  name: 'Home',
  setup() {
    const router = useRouter()
    const { user, isAuthenticated } = useAuth()
    const hotProducts = ref([])
    const isLoadingProducts = ref(false)

    const loadHotProducts = async () => {
      isLoadingProducts.value = true
      try {
        const products = await productService.getHotProducts(6)
        hotProducts.value = products
      } catch (error) {
        console.error('加载热门商品失败:', error)
        // 静默失败，不显示错误提示
      } finally {
        isLoadingProducts.value = false
      }
    }

    const goToProduct = (productId) => {
      router.push(`/products/${productId}`)
    }

    onMounted(() => {
      loadHotProducts()
    })

    return {
      user,
      isAuthenticated,
      hotProducts,
      isLoadingProducts,
      goToProduct
    }
  }
}
</script>

<style scoped>
.home-page {
  min-height: calc(100vh - 80px);
}

/* Hero Section */
.hero-section {
  position: relative;
  padding: var(--spacing-xl) 0;
  min-height: 70vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  align-items: center;
}

.hero-title {
  font-size: 3rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: var(--spacing-lg);
  line-height: 1.5;
}

.hero-features {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.feature-icon {
  font-size: 1.2rem;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-md);
}

.hero-btn {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1.1rem;
}

/* Hero Visual */
.hero-visual {
  display: flex;
  justify-content: center;
}

.hero-window {
  width: 100%;
  max-width: 400px;
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  overflow: hidden;
}

.window-header {
  background-color: var(--secondary-surface);
  border-bottom: var(--border-width) solid var(--neutral-black);
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.window-controls {
  display: flex;
  gap: 6px;
}

.control-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--neutral-black);
  border: 1px solid var(--neutral-black);
}

.window-title {
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
}

.window-content {
  padding: var(--spacing-lg);
}

.loading-products,
.no-products {
  text-align: center;
  padding: var(--spacing-lg);
  color: #666;
}

.mock-product {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  background-color: var(--neutral-white);
  cursor: pointer;
  transition: all 0.2s ease;
}

.mock-product:hover {
  transform: translate(-1px, -1px);
  box-shadow: 2px 2px 0 var(--neutral-black);
}

.mock-product:last-child {
  margin-bottom: 0;
}

.product-logo {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--secondary-surface);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.product-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info h3 {
  font-size: 1rem;
  margin: 0 0 var(--spacing-xs) 0;
  font-weight: var(--font-weight-medium);
}

.product-price {
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
  color: var(--accent-highlight);
  margin: 0 0 var(--spacing-xs) 0;
}

.product-status {
  font-size: 0.8rem;
  background-color: var(--secondary-surface);
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid var(--neutral-black);
  text-transform: uppercase;
  font-weight: var(--font-weight-medium);
}

/* Hero Decorations */
.hero-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

.decoration-grid {
  position: absolute;
  top: 10%;
  right: 5%;
  width: 150px;
  height: 150px;
  background-image:
    linear-gradient(var(--neutral-black) 1px, transparent 1px),
    linear-gradient(90deg, var(--neutral-black) 1px, transparent 1px);
  background-size: 15px 15px;
  opacity: 0.2;
}

.decoration-shape {
  position: absolute;
  border: var(--border-width) solid var(--neutral-black);
}

.shape-1 {
  width: 80px;
  height: 80px;
  background-color: var(--accent-highlight);
  border-radius: 50%;
  top: 20%;
  left: 5%;
  opacity: 0.7;
}

.shape-2 {
  width: 60px;
  height: 60px;
  background-color: var(--secondary-surface);
  bottom: 20%;
  left: 10%;
  transform: rotate(45deg);
  opacity: 0.7;
}

.shape-3 {
  width: 100px;
  height: 50px;
  background-color: var(--primary-background);
  top: 60%;
  right: 10%;
  opacity: 0.6;
}

/* Features Section */
.features-section {
  padding: var(--spacing-xl) 0;
  background-color: var(--neutral-white);
  border-top: var(--border-width) solid var(--neutral-black);
  border-bottom: var(--border-width) solid var(--neutral-black);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: var(--spacing-xl);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.feature-card {
  background-color: var(--secondary-surface);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-offset) var(--shadow-offset) 0 var(--neutral-black);
  padding: var(--spacing-lg);
  transition: all 0.2s ease;
}

.feature-card:hover {
  transform: translate(-2px, -2px);
  box-shadow: calc(var(--shadow-offset) + 2px) calc(var(--shadow-offset) + 2px) 0 var(--neutral-black);
}

.feature-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.feature-icon-large {
  font-size: 2.5rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--neutral-white);
  border: var(--border-width) solid var(--neutral-black);
  border-radius: var(--border-radius);
}

.feature-title {
  font-size: 1.5rem;
  margin: 0;
  font-weight: var(--font-weight-bold);
}

.feature-description {
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
  color: #333;
}

/* CTA Section */
.cta-section {
  padding: var(--spacing-xl) 0;
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
}

.cta-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: var(--spacing-xl);
}

.cta-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.cta-btn {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1.2rem;
  min-width: 140px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-features {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-btn {
    width: 100%;
    max-width: 300px;
  }

  .section-title {
    font-size: 2rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-btn {
    width: 100%;
    max-width: 280px;
  }

  .feature-header {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.8rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .cta-title {
    font-size: 1.8rem;
  }
}
</style>
